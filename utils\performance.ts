/**
 * 性能监控工具
 * 用于监控关键操作的性能指标
 */

export interface PerformanceMetrics {
  startTime: number;
  endTime?: number;
  duration?: number;
  memoryUsage?: {
    used: number;
    total: number;
  };
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map();

  /**
   * 开始监控一个操作
   */
  start(operationName: string): void {
    const startTime = performance.now();
    this.metrics.set(operationName, {
      startTime,
      memoryUsage: this.getMemoryUsage()
    });
    console.log(`🚀 开始监控: ${operationName}`);
  }

  /**
   * 结束监控一个操作
   */
  end(operationName: string): PerformanceMetrics | null {
    const metric = this.metrics.get(operationName);
    if (!metric) {
      console.warn(`⚠️ 未找到操作: ${operationName}`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;
    const endMemoryUsage = this.getMemoryUsage();

    const finalMetric: PerformanceMetrics = {
      ...metric,
      endTime,
      duration,
      memoryUsage: endMemoryUsage
    };

    this.metrics.set(operationName, finalMetric);

    // 输出性能报告
    console.log(`✅ 操作完成: ${operationName}`);
    console.log(`⏱️ 耗时: ${duration.toFixed(2)}ms`);
    if (endMemoryUsage) {
      console.log(`💾 内存使用: ${(endMemoryUsage.used / 1024 / 1024).toFixed(2)}MB`);
    }

    return finalMetric;
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize
      };
    }
    return undefined;
  }

  /**
   * 获取所有性能指标
   */
  getAllMetrics(): Map<string, PerformanceMetrics> {
    return new Map(this.metrics);
  }

  /**
   * 清除所有性能指标
   */
  clear(): void {
    this.metrics.clear();
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const metrics = Array.from(this.metrics.entries());
    if (metrics.length === 0) {
      return '📊 暂无性能数据';
    }

    let report = '📊 性能报告\n';
    report += '=' .repeat(50) + '\n';

    metrics.forEach(([name, metric]) => {
      report += `\n🔍 操作: ${name}\n`;
      if (metric.duration !== undefined) {
        report += `⏱️ 耗时: ${metric.duration.toFixed(2)}ms\n`;
        
        // 性能评级
        if (metric.duration < 100) {
          report += `🟢 性能: 优秀\n`;
        } else if (metric.duration < 500) {
          report += `🟡 性能: 良好\n`;
        } else if (metric.duration < 1000) {
          report += `🟠 性能: 一般\n`;
        } else {
          report += `🔴 性能: 需要优化\n`;
        }
      }
      
      if (metric.memoryUsage) {
        const memoryMB = (metric.memoryUsage.used / 1024 / 1024).toFixed(2);
        report += `💾 内存: ${memoryMB}MB\n`;
      }
      report += '-'.repeat(30) + '\n';
    });

    return report;
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

/**
 * 性能装饰器 - 自动监控函数执行时间
 */
export function measurePerformance(operationName?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const name = operationName || `${target.constructor.name}.${propertyName}`;

    descriptor.value = async function (...args: any[]) {
      performanceMonitor.start(name);
      try {
        const result = await method.apply(this, args);
        performanceMonitor.end(name);
        return result;
      } catch (error) {
        performanceMonitor.end(name);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * 简化的性能测量函数
 */
export async function measureAsync<T>(
  operationName: string,
  operation: () => Promise<T>
): Promise<T> {
  performanceMonitor.start(operationName);
  try {
    const result = await operation();
    performanceMonitor.end(operationName);
    return result;
  } catch (error) {
    performanceMonitor.end(operationName);
    throw error;
  }
}

/**
 * 同步版本的性能测量
 */
export function measure<T>(
  operationName: string,
  operation: () => T
): T {
  performanceMonitor.start(operationName);
  try {
    const result = operation();
    performanceMonitor.end(operationName);
    return result;
  } catch (error) {
    performanceMonitor.end(operationName);
    throw error;
  }
}

/**
 * 检查设备性能等级
 */
export function getDevicePerformanceLevel(): 'high' | 'medium' | 'low' {
  // 基于硬件并发数判断设备性能
  const cores = navigator.hardwareConcurrency || 2;
  
  // 基于内存判断（如果可用）
  const memory = (navigator as any).deviceMemory;
  
  if (cores >= 8 || (memory && memory >= 8)) {
    return 'high';
  } else if (cores >= 4 || (memory && memory >= 4)) {
    return 'medium';
  } else {
    return 'low';
  }
}

/**
 * 根据设备性能调整配置
 */
export function getOptimalConfig() {
  const performanceLevel = getDevicePerformanceLevel();

  switch (performanceLevel) {
    case 'high':
      return {
        scale: Math.max(2, window.devicePixelRatio || 1), // 最小2倍缩放确保清晰度
        imageTimeout: 15000, // 增加超时时间以支持高质量渲染
        maxConcurrentImages: 10
      };
    case 'medium':
      return {
        scale: Math.max(2, Math.min(window.devicePixelRatio || 1, 3)), // 2-3倍缩放
        imageTimeout: 12000,
        maxConcurrentImages: 5
      };
    case 'low':
      return {
        scale: 2, // 即使在低性能设备上也保证2倍缩放
        imageTimeout: 8000,
        maxConcurrentImages: 3
      };
  }
}
