import html2canvas from 'html2canvas-pro';
import { performanceMonitor, measureAsync, getOptimalConfig } from './performance';
import { createUserFriendlyError, checkBrowserCompatibility } from './errorHandling';

/**
 * 生成带时间戳的文件名
 * 格式：fake-text-message-YYYYMMDD-HHMMSS.png
 */
export const generateFileName = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `fake-text-message-${year}${month}${day}-${hours}${minutes}${seconds}.png`;
};

/**
 * 预加载元素中的所有图片
 * @param element - 包含图片的DOM元素
 * @returns Promise<void> - 当所有图片加载完成时resolve
 */
const preloadImages = async (element: HTMLElement): Promise<void> => {
  const images = element.querySelectorAll('img');

  if (images.length === 0) {
    console.log('没有找到需要预加载的图片');
    return;
  }

  console.log(`开始预加载 ${images.length} 张图片`);

  const imagePromises = Array.from(images).map((img) => {
    return new Promise<void>((resolve, reject) => {
      // 如果图片已经加载完成
      if (img.complete && img.naturalHeight !== 0) {
        console.log('图片已加载:', img.src.substring(0, 50) + '...');
        resolve();
        return;
      }

      // 设置加载超时
      const timeout = setTimeout(() => {
        console.warn('图片加载超时:', img.src.substring(0, 50) + '...');
        resolve(); // 即使超时也继续，避免阻塞整个流程
      }, 10000);

      img.onload = () => {
        clearTimeout(timeout);
        console.log('图片加载成功:', img.src.substring(0, 50) + '...');
        resolve();
      };

      img.onerror = () => {
        clearTimeout(timeout);
        console.warn('图片加载失败:', img.src.substring(0, 50) + '...');
        resolve(); // 即使失败也继续，避免阻塞整个流程
      };

      // 如果图片还没有src，也直接resolve
      if (!img.src) {
        clearTimeout(timeout);
        resolve();
      }
    });
  });

  await Promise.all(imagePromises);
  console.log('所有图片预加载完成');
};

/**
 * 将canvas转换为blob并下载
 * @param canvas - 要下载的canvas元素
 * @param filename - 文件名
 */
const downloadCanvasAsImage = (canvas: HTMLCanvasElement, filename: string): void => {
  // 使用最高质量的PNG格式
  canvas.toBlob((blob) => {
    if (!blob) {
      throw new Error('无法生成图片文件');
    }

    console.log('生成的图片信息:', {
      size: `${(blob.size / 1024 / 1024).toFixed(2)}MB`,
      type: blob.type,
      canvasSize: `${canvas.width}x${canvas.height}`,
      pixelRatio: window.devicePixelRatio
    });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    URL.revokeObjectURL(url);
  }, 'image/png', 1.0); // PNG格式，最高质量
};

/**
 * 截图并下载PhonePreview组件
 * @param element - 要截图的DOM元素
 * @param filename - 可选的文件名，如果不提供则自动生成
 */
export const downloadPhonePreview = async (
  element: HTMLElement,
  filename?: string
): Promise<void> => {
  return measureAsync('完整截图流程', async () => {
    try {
      // 检查浏览器兼容性
      const compatibility = checkBrowserCompatibility();
      if (!compatibility.compatible) {
        throw new Error(`Browser compatibility issue: ${compatibility.issues.join(', ')}`);
      }

      if (!element) {
        throw new Error('Preview element not found');
      }

      console.log('开始截图流程...');

    // 预加载所有图片
    console.log('步骤1: 预加载图片');
    await measureAsync('图片预加载', () => preloadImages(element));

    // 等待字体加载完成
    console.log('步骤2: 等待字体加载');
    await document.fonts.ready;

    // 等待渲染完成，根据设备性能调整等待时间
    console.log('步骤3: 等待渲染完成');
    const optimalConfig = getOptimalConfig();
    const waitTime = optimalConfig.scale > 1 ? 800 : 500; // 高分辨率设备需要更多时间
    await new Promise(resolve => setTimeout(resolve, waitTime));

    // 获取元素的计算样式和尺寸
    const computedStyle = window.getComputedStyle(element);
    const rect = element.getBoundingClientRect();

    console.log('步骤4: 获取元素尺寸', {
      offsetWidth: element.offsetWidth,
      offsetHeight: element.offsetHeight,
      clientWidth: element.clientWidth,
      clientHeight: element.clientHeight,
      rectWidth: rect.width,
      rectHeight: rect.height
    });

    // 计算实际的元素尺寸（考虑CSS缩放）
    const transform = window.getComputedStyle(element).transform;
    let actualScale = 1;

    // 解析CSS transform中的scale值
    if (transform && transform !== 'none') {
      const matrix = transform.match(/matrix\(([^)]+)\)/);
      if (matrix) {
        const values = matrix[1].split(',').map(v => parseFloat(v.trim()));
        actualScale = values[0]; // matrix的第一个值是scaleX
      }
    }

    // 获取原始尺寸（未缩放前的尺寸）
    const originalWidth = 316; // PhonePreview的原始宽度
    const originalHeight = 684; // PhonePreview的原始高度

    // 计算高质量的缩放比例
    // 使用更高的缩放比例来确保清晰度，最小2倍，最大4倍
    const highQualityScale = Math.max(2, Math.min(4, (window.devicePixelRatio || 1) * 2));

    console.log('缩放信息:', {
      cssScale: actualScale,
      devicePixelRatio: window.devicePixelRatio,
      highQualityScale: highQualityScale,
      originalSize: { width: originalWidth, height: originalHeight },
      elementSize: { width: element.offsetWidth, height: element.offsetHeight }
    });

    // 优化的html2canvas配置 - 高质量渲染
    const canvasOptions = {
      useCORS: true,
      allowTaint: true,
      logging: false,
      scale: highQualityScale, // 使用高质量缩放
      backgroundColor: '#FFFFFF',
      width: originalWidth, // 使用原始尺寸而不是缩放后的尺寸
      height: originalHeight,
      imageTimeout: optimalConfig.imageTimeout,
      removeContainer: true,
      // 确保字体正确渲染
      onclone: (clonedDoc: Document) => {
        // 临时移除CSS缩放，确保以原始尺寸渲染
        const clonedElement = clonedDoc.querySelector('[class*="scale-"]') as HTMLElement;
        if (clonedElement) {
          // 移除所有缩放相关的类
          clonedElement.className = clonedElement.className
            .replace(/scale-\d+/g, '')
            .replace(/xs:scale-\d+/g, '')
            .replace(/sm:scale-\d+/g, '')
            .replace(/md:scale-\d+/g, '')
            .replace(/lg:scale-\d+/g, '')
            .replace(/xl:scale-\d+/g, '');

          // 确保元素使用原始尺寸
          clonedElement.style.transform = 'none';
          clonedElement.style.width = `${originalWidth}px`;
          clonedElement.style.height = `${originalHeight}px`;
        }

        // 确保字体样式被正确复制
        const style = clonedDoc.createElement('style');
        style.textContent = `
          @import url('https://fonts.googleapis.com/css2?family=Geist:wght@400;500;600;700&display=swap');
          * {
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
          }
          /* 确保时间戳样式正确 */
          span[class*="text-[11px]"] {
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif !important;
            color: #A8A8A8 !important;
            font-weight: 400 !important;
            font-size: 11px !important;
          }
          /* 移除所有缩放变换 */
          * {
            transform: none !important;
          }
        `;
        clonedDoc.head.appendChild(style);
      }
    };

    console.log('步骤5: 开始html2canvas渲染', canvasOptions);

    // 使用html2canvas截图，添加性能监控
    const canvas = await measureAsync('html2canvas渲染', () =>
      html2canvas(element, canvasOptions)
    );

    console.log('步骤6: 渲染完成，Canvas尺寸:', {
      width: canvas.width,
      height: canvas.height
    });

    // 生成文件名
    const finalFilename = filename || generateFileName();

    console.log('步骤7: 开始下载，文件名:', finalFilename);

    // 下载图片
    downloadCanvasAsImage(canvas, finalFilename);

    console.log('截图流程完成');

    } catch (error) {
      console.error('下载失败详细信息:', {
        error: error,
        message: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined
      });

      // 创建用户友好的错误消息
      const userFriendlyError = createUserFriendlyError(
        error instanceof Error ? error : '下载过程中发生未知错误',
        'Download'
      );

      throw new Error(userFriendlyError.message);
    }
  });
};
